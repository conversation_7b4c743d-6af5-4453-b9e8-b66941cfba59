import { Injectable } from '@angular/core';
import { environment } from '../../../environments/environment';
import {
  HttpClient,
  HttpErrorResponse,
  HttpParams,
} from '@angular/common/http';
import { AuthService } from './auth.service';
import { catchError, Observable, throwError, map } from 'rxjs';
import { Event, EventRequest, EventType, Category } from '../Models/events';
import { PaginatedResponse } from '../Models/pagination';
import { EventPaginationParams } from '../Models/pagination.events.interface';

interface ApiResponse<T> {
  isSuccess: boolean;
  message: string;
  data: T;
  errors?: any;
}

@Injectable({
  providedIn: 'root',
})
export class EventsService {
  private readonly API_URL = `${environment.apiUrl}/api/Events`;

  constructor(
    private readonly http: HttpClient,
    private readonly authService: AuthService,
  ) {}

  getEvents(
    params: EventPaginationParams,
  ): Observable<PaginatedResponse<Event>> {
    let httpParams = new HttpParams()
      .set('pageNumber', params.pageNumber.toString())
      .set('pageSize', params.pageSize.toString());

    if (params.sortField) {
      httpParams = httpParams
        .set('sortField', params.sortField)
        .set('sortOrder', params.sortOrder || 'asc');
    }

    if (params.filters) {
      // Search term filter
      if (params.filters.searchTerm) {
        httpParams = httpParams.set('searchTerm', params.filters.searchTerm);
      }

      // Event start date filter - map eventStartDateFrom to eventStartDate
      if (params.filters.eventStartDateFrom) {
        try {
          // Use the same logic as working date filters - direct toISOString()
          const dateValue = params.filters.eventStartDateFrom.toISOString();
          httpParams = httpParams.set('eventStartDate', dateValue);
        } catch (error) {
          console.error(
            'Error converting eventStartDateFrom to ISO string:',
            error,
          );
        }
      }

      // Legacy eventStartDate support (for backward compatibility)
      if (params.filters.eventStartDate && !params.filters.eventStartDateFrom) {
        try {
          // Use the same logic as working date filters - direct toISOString()
          const dateValue = params.filters.eventStartDate.toISOString();
          httpParams = httpParams.set('eventStartDate', dateValue);
        } catch (error) {
          console.error(
            'Error converting eventStartDate to ISO string:',
            error,
          );
        }
      }

      // Event status filter
      if (params.filters.eventStatus) {
        httpParams = httpParams.set('eventStatus', params.filters.eventStatus);
      }

      // Organizer filter
      if (params.filters.organizer) {
        httpParams = httpParams.set('organizer', params.filters.organizer);
      }

      // Approval status filter
      if (params.filters.approvalStatus) {
        httpParams = httpParams.set(
          'approvalStatus',
          params.filters.approvalStatus,
        );
      }

      // Event type filter - convert numeric value to enum name
      if (params.filters.type) {
        const typeValue =
          typeof params.filters.type === 'string'
            ? params.filters.type
            : this.getEventTypeEnumName(Number(params.filters.type));
        httpParams = httpParams.set('type', typeValue);
      }

      // Category filter - convert numeric value to enum name
      if (params.filters.category) {
        const categoryValue =
          typeof params.filters.category === 'string'
            ? params.filters.category
            : this.getCategoryEnumName(Number(params.filters.category));
        httpParams = httpParams.set('category', categoryValue);
      }

      // Submitted on date filter
      if (params.filters.submittedOn) {
        try {
          const dateValue = params.filters.submittedOn.toISOString();
          httpParams = httpParams.set('submittedOn', dateValue);
        } catch (error) {
          console.error('Error converting submittedOn to ISO string:', error);
        }
      }

      // Event reviewed on date filter
      if (params.filters.eventReviewedOn) {
        try {
          const dateValue = params.filters.eventReviewedOn.toISOString();
          httpParams = httpParams.set('eventReviewedOn', dateValue);
        } catch (error) {
          console.error(
            'Error converting eventReviewedOn to ISO string:',
            error,
          );
        }
      }
    }

    return this.http
      .get<any>(`${this.API_URL}/GetAllEvents`, {
        params: httpParams,
      })
      .pipe(
        map((response) => {
          // Check if the response is already a PaginatedResponse
          if (
            response &&
            response.items !== undefined &&
            response.totalItems !== undefined
          ) {
            return response;
          }

          // Check if the response is wrapped in an ApiResponse
          if (
            response &&
            response.isSuccess !== undefined &&
            response.data !== undefined
          ) {
            if (response.isSuccess && response.data) {
              return response.data;
            } else {
              throw new Error(response.message || 'Failed to get events');
            }
          }

          console.error('Unexpected response format:', response);
          throw new Error('Unexpected response format from server');
        }),
        catchError((err) => {
          console.error('GetAllEvents Error:', err);
          return this.handleError(err);
        }),
      );
  }

  getEventById(id: number): Observable<Event> {
    return this.http
      .get<ApiResponse<Event>>(`${this.API_URL}/GetEventById/${id}`)
      .pipe(
        map((response) => {
          if (response.isSuccess && response.data) {
            return response.data;
          } else {
            throw new Error(response.message || 'Failed to get event details');
          }
        }),
        catchError((err) => this.handleError(err)),
      );
  }

  createEvent(event: EventRequest): Observable<Event> {
    const formData = new FormData();

    // Add all event properties to FormData
    Object.keys(event).forEach((key) => {
      if (key === 'eventImage' && event.eventImage) {
        formData.append('eventImage', event.eventImage, event.eventImage.name);
      } else if (key === 'location') {
        // Handle location object
        Object.keys(event.location).forEach((locationKey) => {
          const value =
            event.location[locationKey as keyof typeof event.location];
          if (value !== undefined && value !== null) {
            formData.append(`location.${locationKey}`, value.toString());
          }
        });
      } else if (key === 'contactDetails') {
        // Handle contactDetails object
        Object.keys(event.contactDetails).forEach((contactKey) => {
          const value =
            event.contactDetails[
              contactKey as keyof typeof event.contactDetails
            ];
          if (value !== undefined && value !== null) {
            formData.append(`contactDetails.${contactKey}`, value.toString());
          }
        });
      } else if (
        key === 'eventStarts' ||
        key === 'eventEnds' ||
        key === 'submittedOn'
      ) {
        // Handle date fields - they should already be formatted as strings
        const value = event[key as keyof typeof event];
        if (value) {
          formData.append(key, value.toString());
        }
      } else {
        // Handle primitive values
        const value = event[key as keyof typeof event];
        if (value !== undefined && value !== null) {
          formData.append(key, value.toString());
        }
      }
    });

    return this.http
      .post<ApiResponse<Event>>(`${this.API_URL}/CreateEvent`, formData)
      .pipe(
        map((response) => {
          if (response.isSuccess && response.data) {
            return response.data;
          } else {
            throw new Error(response.message || 'Failed to create event');
          }
        }),
        catchError((err) => this.handleError(err)),
      );
  }

  updateEvent(event: EventRequest): Observable<Event> {
    const formData = new FormData();

    // Add all event properties to FormData
    Object.keys(event).forEach((key) => {
      if (key === 'eventImage' && event.eventImage) {
        formData.append('eventImage', event.eventImage, event.eventImage.name);
      } else if (key === 'location') {
        // Handle location object
        Object.keys(event.location).forEach((locationKey) => {
          const value =
            event.location[locationKey as keyof typeof event.location];
          if (value !== undefined && value !== null) {
            formData.append(`location.${locationKey}`, value.toString());
          }
        });
      } else if (key === 'contactDetails') {
        // Handle contactDetails object
        Object.keys(event.contactDetails).forEach((contactKey) => {
          const value =
            event.contactDetails[
              contactKey as keyof typeof event.contactDetails
            ];
          if (value !== undefined && value !== null) {
            formData.append(`contactDetails.${contactKey}`, value.toString());
          }
        });
      } else if (
        key === 'eventStarts' ||
        key === 'eventEnds' ||
        key === 'submittedOn'
      ) {
        // Handle date fields - they should already be formatted as strings
        const value = event[key as keyof typeof event];
        if (value) {
          formData.append(key, value.toString());
        }
      } else {
        // Handle primitive values
        const value = event[key as keyof typeof event];
        if (value !== undefined && value !== null) {
          formData.append(key, value.toString());
        }
      }
    });

    return this.http
      .put<ApiResponse<Event>>(`${this.API_URL}/UpdateEvent`, formData)
      .pipe(
        map((response) => {
          if (response.isSuccess && response.data) {
            return response.data;
          } else {
            throw new Error(response.message || 'Failed to update event');
          }
        }),
        catchError((err) => this.handleError(err)),
      );
  }

  deleteEvent(id: number): Observable<boolean> {
    return this.http
      .delete<ApiResponse<boolean>>(`${this.API_URL}/DeleteEvent/${id}`)
      .pipe(
        map((response) => {
          if (response.isSuccess) {
            return response.data;
          } else {
            throw new Error(response.message || 'Failed to delete event');
          }
        }),
        catchError((err) => this.handleError(err)),
      );
  }

  approveEvent(id: number): Observable<boolean> {
    return this.http
      .post<ApiResponse<boolean>>(`${this.API_URL}/ApproveEvent/${id}`, {})
      .pipe(
        map((response) => {
          if (response.isSuccess) {
            return response.data;
          } else {
            throw new Error(response.message || 'Failed to approve event');
          }
        }),
        catchError((err) => {
          console.error('Error in approveEvent service call:', err);
          return this.handleError(err);
        }),
      );
  }

  rejectEvent(id: number, rejectionReason: string): Observable<boolean> {
    return this.http
      .post<
        ApiResponse<boolean>
      >(`${this.API_URL}/RejectEvent/${id}`, { rejectionReason })
      .pipe(
        map((response) => {
          if (response.isSuccess) {
            return response.data;
          } else {
            throw new Error(response.message || 'Failed to reject event');
          }
        }),
        catchError((err) => {
          console.error('Error in rejectEvent service call:', err);
          return this.handleError(err);
        }),
      );
  }

  submitEvent(id: number): Observable<boolean> {
    return this.http
      .post<ApiResponse<boolean>>(`${this.API_URL}/SubmitEvent/${id}`, {})
      .pipe(
        map((response) => {
          if (response.isSuccess) {
            return response.data;
          } else {
            throw new Error(response.message || 'Failed to submit event');
          }
        }),
        catchError((err) => this.handleError(err)),
      );
  }

  cancelEvent(id: number): Observable<boolean> {
    return this.http
      .post<ApiResponse<boolean>>(`${this.API_URL}/CancelEvent/${id}`, {})
      .pipe(
        map((response) => {
          if (response.isSuccess) {
            return response.data;
          } else {
            throw new Error(response.message || 'Failed to cancel event');
          }
        }),
        catchError((err) => this.handleError(err)),
      );
  }

  // The checkAndDeleteExpiredEvents method has been removed
  // as this functionality is now handled by a server-side background service

  /**
   * Convert EventType numeric value to enum name string
   */
  private getEventTypeEnumName(typeValue: number): string {
    switch (typeValue) {
      case EventType.AppearanceOrSigning:
        return 'AppearanceOrSigning';
      case EventType.Attraction:
        return 'Attraction';
      case EventType.CampTripOrRetreat:
        return 'CampTripOrRetreat';
      case EventType.ClassTrainingOrWorkshop:
        return 'ClassTrainingOrWorkshop';
      case EventType.ConcertOrPerformance:
        return 'ConcertOrPerformance';
      case EventType.Conference:
        return 'Conference';
      case EventType.Convention:
        return 'Convention';
      case EventType.DinnerOrGala:
        return 'DinnerOrGala';
      case EventType.FestivalOrFair:
        return 'FestivalOrFair';
      case EventType.GamesOrCompetition:
        return 'GamesOrCompetition';
      case EventType.MeetingOrNetworkingEvent:
        return 'MeetingOrNetworkingEvent';
      case EventType.Other:
        return 'Other';
      case EventType.PartyOrSocialGathering:
        return 'PartyOrSocialGathering';
      case EventType.Rally:
        return 'Rally';
      case EventType.Screening:
        return 'Screening';
      case EventType.SeminarOrTalk:
        return 'SeminarOrTalk';
      case EventType.Tour:
        return 'Tour';
      case EventType.Tournament:
        return 'Tournament';
      case EventType.TradeShowConsumerShowOrExpo:
        return 'TradeShowConsumerShowOrExpo';
      default:
        return typeValue.toString();
    }
  }

  /**
   * Convert Category numeric value to enum name string
   */
  private getCategoryEnumName(categoryValue: number): string {
    switch (categoryValue) {
      case Category.CareersAndEmployment:
        return 'CareersAndEmployment';
      case Category.CommunityResources:
        return 'CommunityResources';
      case Category.EarlyChildhood:
        return 'EarlyChildhood';
      case Category.HealthWellness:
        return 'HealthWellness';
      case Category.MaternalHealthCare:
        return 'MaternalHealthCare';
      case Category.RentalHousing:
        return 'RentalHousing';
      default:
        return categoryValue.toString();
    }
  }

  private handleError(error: HttpErrorResponse) {
    let errorMessage = 'An unknown error occurred';

    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Error: ${error.error.message}`;
      console.error('Client-side error:', error.error);
    } else {
      // Server-side error
      console.error('Server-side error:', error);

      if (error.status === 0) {
        errorMessage =
          'Could not connect to the server. Please try again later.';
      } else if (error.status === 401) {
        errorMessage = 'Unauthorized. Please log in again.';
        this.authService.logout();
      } else if (error.status === 403) {
        errorMessage = 'You do not have permission to perform this action.';
        // Return the original HttpErrorResponse so component can access the status code
        return throwError(() => error);
      } else if (error.status === 413) {
        // Request Entity Too Large - typically for file size issues
        errorMessage = 'File size exceeds the maximum allowed size of 5 MB';
        console.error('File size error:', errorMessage);
      } else if (error.error && error.error.message) {
        errorMessage = error.error.message;

        // Check if the error message contains file size information
        if (
          errorMessage.toLowerCase().includes('file size') ||
          errorMessage.toLowerCase().includes('exceeds') ||
          errorMessage.toLowerCase().includes('too large')
        ) {
          errorMessage = 'File size exceeds the maximum allowed size of 5 MB';
        }

        console.error('Error message from server:', error.error.message);
      } else {
        errorMessage = `Error Code: ${error.status}, Message: ${error.message}`;
      }
    }
    console.error('Final error message:', errorMessage);
    return throwError(() => new Error(errorMessage));
  }
}
